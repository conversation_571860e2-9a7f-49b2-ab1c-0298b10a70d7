import React, { useState, useEffect, useRef } from 'react';
import './index.css';
import { LanguageProvider, useLanguage } from './LanguageContext.jsx';
import LanguageSelectionDialog from './LanguageSelectionDialog.jsx';

// Minimal App Content Component
function AppContent() {
  const { t, isLanguageSelected, setIsLanguageSelected, currentLanguage } = useLanguage();

  // Basic state
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [isLoggedIn, setIsLoggedIn] = useState(true);
  const [dashboardScannerInput, setDashboardScannerInput] = useState('');
  const dashboardScannerRef = useRef(null);

  // Handle language selection
  const handleLanguageSelected = (langCode) => {
    console.log('Language selected:', langCode);
    setIsLanguageSelected(true);
  };

  // Navigation function
  const navigateToPage = (page) => {
    setCurrentPage(page);
  };

  // Simple scanner input handler
  const handleDashboardScannerInput = (e) => {
    setDashboardScannerInput(e.target.value);
  };

  const handleDashboardScannerKeyPress = (e) => {
    if (e.key === 'Enter') {
      console.log('Barcode scanned:', dashboardScannerInput);
      setDashboardScannerInput('');
    }
  };

  // Show language selection dialog if language not selected
  if (!isLanguageSelected) {
    return <LanguageSelectionDialog onLanguageSelected={handleLanguageSelected} />;
  }

  return (
    <div className="accounting-system">
      {/* Sidebar */}
      <div className={`sidebar page-${currentPage}`}>
        <div className="sidebar-header">
          <div className="system-logo">
            <img
              src="./assets/logo2png.png"
              alt="نظام المحاسبي"
              className="logo-image"
            />
            <div className="system-info">
              <h1>{t('systemName', 'نظام المحاسبي')}</h1>
              <p>{t('systemDescription', 'النظام المحاسبي المتكامل لإدارة أعمالك')}</p>
            </div>
          </div>
        </div>

        <nav className="sidebar-nav">
          <button
            className={`nav-item ${currentPage === 'dashboard' ? 'active' : ''}`}
            onClick={() => navigateToPage('dashboard')}
          >
            <span className="nav-icon">🏠</span>
            {t('dashboard', 'لوحة التحكم')}
          </button>
          <button
            className={`nav-item ${currentPage === 'sales' ? 'active' : ''}`}
            onClick={() => navigateToPage('sales')}
          >
            <span className="nav-icon">🛒</span>
            {t('sales', 'المبيعات')}
          </button>
          <button
            className={`nav-item ${currentPage === 'purchases' ? 'active' : ''}`}
            onClick={() => navigateToPage('purchases')}
          >
            <span className="nav-icon">📦</span>
            {t('purchases', 'المشتريات')}
          </button>
          <button
            className={`nav-item ${currentPage === 'customers' ? 'active' : ''}`}
            onClick={() => navigateToPage('customers')}
          >
            <span className="nav-icon">👥</span>
            {t('customers', 'العملاء')}
          </button>
          <button
            className={`nav-item ${currentPage === 'inventory' ? 'active' : ''}`}
            onClick={() => navigateToPage('inventory')}
          >
            <span className="nav-icon">📊</span>
            {t('products', 'المخزون')}
          </button>
          <button
            className={`nav-item ${currentPage === 'reports' ? 'active' : ''}`}
            onClick={() => navigateToPage('reports')}
          >
            <span className="nav-icon">📈</span>
            {t('reports', 'التقارير')}
          </button>
          <button
            className={`nav-item ${currentPage === 'settings' ? 'active' : ''}`}
            onClick={() => navigateToPage('settings')}
          >
            <span className="nav-icon">⚙️</span>
            {t('settings', 'الإعدادات')}
          </button>
        </nav>
      </div>

      {/* Main Content */}
      <main className="main-content">
        {currentPage === 'dashboard' && (
          <div className="dashboard">
            <div className={`page-header ${currentLanguage !== 'ar' ? 'page-header-ltr-split' : ''}`}>
              <div className="page-title-section">
                <h1>🏠 {t('dashboard', 'لوحة التحكم')}</h1>
              </div>
            </div>

            {/* Simplified Dashboard Content */}
            <div className="dashboard-content">
              <div className="dashboard-scanner">
                <h3>📷 {t('scanBarcode', 'مسح الباركود')}</h3>
                <input
                  type="text"
                  placeholder={t('scanBarcodeToAddProduct', 'امسح الباركود')}
                  value={dashboardScannerInput}
                  onChange={handleDashboardScannerInput}
                  onKeyDown={handleDashboardScannerKeyPress}
                  className="barcode-input"
                  ref={dashboardScannerRef}
                  autoFocus
                />
              </div>
              
              {/* Quick Actions */}
              <div className="dashboard-actions">
                <button className="btn btn-primary">
                  🛒 {t('newSalesInvoice', 'فاتورة مبيعات جديدة')}
                </button>
                <button className="btn btn-secondary">
                  📦 {t('addNewProduct', 'إضافة منتج جديد')}
                </button>
              </div>
            </div>
          </div>
        )}

        {currentPage === 'sales' && (
          <div className="sales-page">
            <h1>🛒 {t('sales', 'المبيعات')}</h1>
            <p>Sales page content will be here</p>
          </div>
        )}

        {currentPage === 'purchases' && (
          <div className="purchases-page">
            <h1>📦 {t('purchases', 'المشتريات')}</h1>
            <p>Purchases page content will be here</p>
          </div>
        )}

        {currentPage === 'customers' && (
          <div className="customers-page">
            <h1>👥 {t('customers', 'العملاء')}</h1>
            <p>Customers page content will be here</p>
          </div>
        )}

        {currentPage === 'inventory' && (
          <div className="inventory-page">
            <h1>📊 {t('products', 'المخزون')}</h1>
            <p>Inventory page content will be here</p>
          </div>
        )}

        {currentPage === 'reports' && (
          <div className="reports-page">
            <h1>📈 {t('reports', 'التقارير')}</h1>
            <p>Reports page content will be here</p>
          </div>
        )}

        {currentPage === 'settings' && (
          <div className="settings-page">
            <h1>⚙️ {t('settings', 'الإعدادات')}</h1>
            <p>Settings page content will be here</p>
          </div>
        )}
      </main>
    </div>
  );
}

// Main App Wrapper with Language Provider
function App() {
  return (
    <LanguageProvider>
      <AppContent />
    </LanguageProvider>
  );
}

export default App;
