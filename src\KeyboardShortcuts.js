/**
 * ⌨️ Keyboard Shortcuts Manager for iCalDZ Accounting System
 * Provides global keyboard shortcuts with context awareness
 *
 * Features:
 * - F1: New Sales Invoice (🛒 فاتورة مبيعات جديدة)
 * - F2: Save, Close & Print Invoice (💾🖨️ حفظ وإغلاق وطباعة الفاتورة) - Smart behavior
 * - F3: Quick Search (🔍 البحث السريع)
 * - F4: Print Invoice (🖨️ طباعة الفاتورة) - Only in invoice window
 * - F5: Refresh Data (🔄 تحديث البيانات)
 * - ESC: Close Window/Exit App (❌ إغلاق النافذة/الخروج من التطبيق)
 *
 * 🔧 BARCODE SCANNER FIX:
 * - Automatically detects barcode input fields and prevents keyboard shortcut interference
 * - Protects barcode scanning functionality from global event handlers
 * - Maintains compatibility with all barcode scanner tools and devices
 */

import { SoundManager } from './SoundManager.js';

class KeyboardShortcutsClass {
  constructor() {
    this.isEnabled = true;
    this.activeWindow = 'dashboard'; // dashboard, sales, invoice, etc.
    this.callbacks = {};
    this.isInitialized = false;
    this.lastKeyTime = 0;
    this.debounceDelay = 300; // 300ms debounce to prevent double triggers
    this.barcodeProtectionEnabled = true; // 🔧 BARCODE SCANNER FIX: Enable barcode protection by default

    // 🔧 LONG-TERM STABILITY: Add runtime monitoring
    this.startTime = Date.now();
    this.eventCount = 0;
    this.barcodeProtectionCount = 0;
    this.lastCleanup = Date.now();

    // Shortcut definitions
    this.shortcuts = {
      F1: {
        key: 'F1',
        description: '🛒 فاتورة مبيعات جديدة',
        action: 'newSalesInvoice',
        contexts: ['sales'], // Only available on sales page
        sound: 'newInvoice'
      },
      F2: {
        key: 'F2',
        description: '💾🖨️ حفظ وإغلاق وطباعة',
        action: 'saveInvoice',
        contexts: ['dashboard', 'sales', 'inventory', 'customers', 'reports', 'salesModal', 'invoiceModal'], // Available everywhere
        sound: 'saveInvoice'
      },
      F3: {
        key: 'F3',
        description: '➕ إضافة منتج',
        action: 'addProduct',
        contexts: ['salesModal'], // Only in sales modal
        sound: 'addProduct'
      },
      F4: {
        key: 'F4',
        description: '🖨️ طباعة الفاتورة',
        action: 'printInvoice',
        contexts: ['salesModal', 'invoiceModal'], // Only in invoice windows
        sound: 'printInvoice'
      },
      F5: {
        key: 'F5',
        description: '🔄 تحديث البيانات',
        action: 'refreshData',
        contexts: ['dashboard', 'sales', 'inventory', 'customers', 'reports'],
        sound: 'refresh'
      },
      Escape: {
        key: 'ESC',
        description: '❌ إغلاق ذكي/خروج',
        action: 'closeWindow',
        contexts: ['dashboard', 'sales', 'inventory', 'customers', 'reports', 'salesModal', 'invoiceModal', 'productModal', 'customerModal'], // Available everywhere
        sound: 'closeWindow'
      }
    };

    this.init();
  }

  /**
   * Initialize keyboard shortcuts
   */
  init() {
    if (this.isInitialized) return;

    try {
      // Store bound functions for proper cleanup
      this.boundHandleKeyDown = this.handleKeyDown.bind(this);
      this.boundPreventDefaults = this.preventDefaults.bind(this);

      // Add global event listener
      document.addEventListener('keydown', this.boundHandleKeyDown, true);

      // Prevent browser default behavior for function keys
      document.addEventListener('keydown', this.boundPreventDefaults, true);

      this.isInitialized = true;
      console.log('⌨️ Keyboard Shortcuts initialized successfully');
    } catch (error) {
      console.error('⌨️ Failed to initialize keyboard shortcuts:', error);
    }
  }

  /**
   * Handle keydown events
   */
  handleKeyDown(event) {
    if (!this.isEnabled) return;

    // 🔧 LONG-TERM STABILITY: Increment event counter and periodic cleanup
    this.eventCount++;
    if (this.eventCount % 1000 === 0) {
      this.performPeriodicCleanup();
    }

    // 🔧 BARCODE SCANNER FIX: Check if user is typing in barcode input fields
    if (this.isBarcodeInputActive(event)) {
      this.barcodeProtectionCount++;
      console.log('⌨️ Barcode input detected - skipping keyboard shortcuts');
      return; // Don't interfere with barcode scanning
    }

    // Debounce to prevent double triggers
    const currentTime = Date.now();
    if (currentTime - this.lastKeyTime < this.debounceDelay) {
      return;
    }

    // Get the key name
    const keyName = this.getKeyName(event);
    if (!keyName) return;

    // Find matching shortcut
    const shortcut = this.shortcuts[keyName];
    if (!shortcut) return;

    // Check if shortcut is available in current context
    if (!this.isShortcutAvailable(shortcut)) {
      console.log(`⌨️ Shortcut ${keyName} not available in context: ${this.activeWindow}`);
      return;
    }

    // Update last key time
    this.lastKeyTime = currentTime;

    // Prevent default browser behavior
    event.preventDefault();
    event.stopPropagation();

    console.log(`⌨️ Executing shortcut: ${keyName} in context: ${this.activeWindow}`);

    // Execute the shortcut
    this.executeShortcut(shortcut, event);
  }

  /**
   * Prevent default browser behavior for function keys and ESC
   */
  preventDefaults(event) {
    // 🔧 BARCODE SCANNER FIX: Don't prevent defaults when barcode input is active
    if (this.isBarcodeInputActive(event)) {
      return; // Allow normal input behavior for barcode scanners
    }

    const functionKeys = ['F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'];
    const keyName = this.getKeyName(event);

    if (functionKeys.includes(keyName) || keyName === 'Escape') {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  /**
   * 🔧 BARCODE SCANNER FIX: Check if user is typing in barcode input fields
   */
  isBarcodeInputActive(event) {
    // If barcode protection is disabled, don't interfere
    if (!this.barcodeProtectionEnabled) {
      return false;
    }

    const target = event.target;

    // Check if the target is an input field
    if (!target || target.tagName !== 'INPUT') {
      return false;
    }

    // Check for barcode input field indicators
    const barcodeIndicators = [
      'barcode-input',           // Class name
      'scanner-input',           // Class name
      'dashboard-scanner',       // ID or class
      'sales-scanner',          // ID or class
      'edit-scanner',           // ID or class
      'product-barcode'         // ID or class
    ];

    // Check class names
    if (target.className) {
      const classNames = target.className.toLowerCase();
      if (barcodeIndicators.some(indicator => classNames.includes(indicator))) {
        return true;
      }
    }

    // Check ID
    if (target.id) {
      const id = target.id.toLowerCase();
      if (barcodeIndicators.some(indicator => id.includes(indicator))) {
        return true;
      }
    }

    // Check placeholder text for barcode-related content
    if (target.placeholder) {
      const placeholder = target.placeholder.toLowerCase();
      const barcodeKeywords = ['barcode', 'باركود', 'scanner', 'مسح', 'scan'];
      if (barcodeKeywords.some(keyword => placeholder.includes(keyword))) {
        return true;
      }
    }

    // Check if input is focused and has barcode-like content
    if (target === document.activeElement && target.value) {
      const value = target.value.trim();
      // Check if value looks like a barcode (alphanumeric, 3+ chars)
      if (value.length >= 3 && /^[a-zA-Z0-9\-_\.]+$/.test(value)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Get standardized key name from event
   */
  getKeyName(event) {
    // Handle ESC key with multiple possible values
    if (event.key === 'Escape' || event.key === 'Esc' || event.keyCode === 27) {
      return 'Escape';
    }

    // Handle function keys
    if (event.key.startsWith('F') && event.key.length <= 3) {
      return event.key;
    }

    return null;
  }

  /**
   * Check if shortcut is available in current context
   */
  isShortcutAvailable(shortcut) {
    return shortcut.contexts.includes(this.activeWindow);
  }

  /**
   * Execute a shortcut action
   */
  async executeShortcut(shortcut, event) {
    try {
      // Play sound feedback
      if (shortcut.sound) {
        SoundManager.play(shortcut.sound, { showNotification: true });
      }

      // Show visual feedback
      this.showShortcutFeedback(shortcut);

      // Execute callback if registered
      const callback = this.callbacks[shortcut.action];
      if (callback && typeof callback === 'function') {
        await callback(event, shortcut);
      } else {
        console.warn(`⌨️ No callback registered for action: ${shortcut.action}`);
      }

    } catch (error) {
      console.error(`⌨️ Error executing shortcut ${shortcut.key}:`, error);
      SoundManager.play('error');
    }
  }

  /**
   * Show visual feedback for shortcut activation
   */
  showShortcutFeedback(shortcut) {
    // Create feedback element
    const feedback = document.createElement('div');
    feedback.className = 'shortcut-feedback';
    feedback.innerHTML = `
      <div class="shortcut-key">${shortcut.key}</div>
      <div class="shortcut-desc">${shortcut.description}</div>
    `;

    feedback.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(22, 160, 133, 0.95);
      color: white;
      padding: 20px 30px;
      border-radius: 15px;
      font-family: 'Cairo', sans-serif;
      text-align: center;
      z-index: 10001;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      animation: shortcutFeedback 1.5s ease-out forwards;
      pointer-events: none;
      backdrop-filter: blur(10px);
    `;

    // Add styles if not present
    if (!document.getElementById('shortcut-feedback-styles')) {
      const styles = document.createElement('style');
      styles.id = 'shortcut-feedback-styles';
      styles.textContent = `
        .shortcut-feedback .shortcut-key {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 8px;
          background: rgba(255, 255, 255, 0.2);
          padding: 8px 16px;
          border-radius: 8px;
          display: inline-block;
        }
        .shortcut-feedback .shortcut-desc {
          font-size: 16px;
          opacity: 0.9;
        }
        @keyframes shortcutFeedback {
          0% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
          }
          20% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.05);
          }
          40% {
            transform: translate(-50%, -50%) scale(1);
          }
          80% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
          }
          100% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9);
          }
        }
      `;
      document.head.appendChild(styles);
    }

    document.body.appendChild(feedback);

    // Remove after animation
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback);
      }
    }, 1500);
  }

  /**
   * Register a callback for a specific action
   */
  registerCallback(action, callback) {
    if (typeof callback !== 'function') {
      console.error(`⌨️ Callback for ${action} must be a function`);
      return;
    }

    this.callbacks[action] = callback;
    console.log(`⌨️ Registered callback for action: ${action}`);
  }

  /**
   * Unregister a callback
   */
  unregisterCallback(action) {
    delete this.callbacks[action];
    console.log(`⌨️ Unregistered callback for action: ${action}`);
  }

  /**
   * Set the active window context
   */
  setActiveWindow(windowName) {
    this.activeWindow = windowName;
    console.log(`⌨️ Active window changed to: ${windowName}`);
  }

  /**
   * Get available shortcuts for current context
   */
  getAvailableShortcuts() {
    return Object.values(this.shortcuts).filter(shortcut =>
      this.isShortcutAvailable(shortcut)
    );
  }

  /**
   * Get all shortcuts
   */
  getAllShortcuts() {
    return Object.values(this.shortcuts);
  }

  /**
   * Enable/disable shortcuts
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    console.log(`⌨️ Keyboard shortcuts ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Toggle shortcuts on/off
   */
  toggle() {
    this.isEnabled = !this.isEnabled;
    return this.isEnabled;
  }

  /**
   * 🔧 BARCODE SCANNER FIX: Enable/disable barcode scanner protection
   */
  setBarcodeProtection(enabled) {
    this.barcodeProtectionEnabled = enabled;
    console.log(`⌨️ Barcode scanner protection ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * 🔧 BARCODE SCANNER FIX: Debug barcode input detection
   */
  debugBarcodeInput(event) {
    const target = event.target;
    console.log('🔍 Barcode Input Debug:', {
      tagName: target?.tagName,
      className: target?.className,
      id: target?.id,
      placeholder: target?.placeholder,
      value: target?.value,
      activeElement: document.activeElement === target,
      isBarcodeInput: this.isBarcodeInputActive(event)
    });
  }

  /**
   * 🔧 LONG-TERM STABILITY: Perform periodic cleanup to prevent memory leaks
   */
  performPeriodicCleanup() {
    const now = Date.now();
    const timeSinceLastCleanup = now - this.lastCleanup;

    // Only cleanup every 10 minutes
    if (timeSinceLastCleanup < 10 * 60 * 1000) {
      return;
    }

    console.log('🧹 Performing periodic cleanup...');

    // Reset counters to prevent overflow
    if (this.eventCount > 100000) {
      this.eventCount = 0;
    }

    if (this.barcodeProtectionCount > 10000) {
      this.barcodeProtectionCount = 0;
    }

    // Update cleanup time
    this.lastCleanup = now;

    // Log stability stats
    const runtime = Math.floor((now - this.startTime) / 1000 / 60); // minutes
    console.log(`📊 Stability Stats: Runtime: ${runtime}min, Events: ${this.eventCount}, Barcode Protection: ${this.barcodeProtectionCount}`);
  }

  /**
   * 🔧 LONG-TERM STABILITY: Get runtime statistics
   */
  getRuntimeStats() {
    const now = Date.now();
    const runtime = now - this.startTime;

    return {
      runtimeMs: runtime,
      runtimeMinutes: Math.floor(runtime / 1000 / 60),
      runtimeHours: Math.floor(runtime / 1000 / 60 / 60),
      runtimeDays: Math.floor(runtime / 1000 / 60 / 60 / 24),
      eventCount: this.eventCount,
      barcodeProtectionCount: this.barcodeProtectionCount,
      lastCleanup: this.lastCleanup,
      memoryUsage: performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
      } : null
    };
  }

  /**
   * Get current status
   */
  getStatus() {
    const runtimeStats = this.getRuntimeStats();

    return {
      isEnabled: this.isEnabled,
      activeWindow: this.activeWindow,
      isInitialized: this.isInitialized,
      barcodeProtectionEnabled: this.barcodeProtectionEnabled, // 🔧 BARCODE SCANNER FIX
      registeredCallbacks: Object.keys(this.callbacks),
      availableShortcuts: this.getAvailableShortcuts().map(s => s.key),
      // 🔧 LONG-TERM STABILITY: Include runtime statistics
      runtime: {
        days: runtimeStats.runtimeDays,
        hours: runtimeStats.runtimeHours,
        minutes: runtimeStats.runtimeMinutes,
        eventCount: runtimeStats.eventCount,
        barcodeProtectionCount: runtimeStats.barcodeProtectionCount,
        memoryUsage: runtimeStats.memoryUsage
      }
    };
  }

  /**
   * Cleanup - remove event listeners
   */
  destroy() {
    if (this.isInitialized && this.boundHandleKeyDown && this.boundPreventDefaults) {
      document.removeEventListener('keydown', this.boundHandleKeyDown, true);
      document.removeEventListener('keydown', this.boundPreventDefaults, true);
      this.boundHandleKeyDown = null;
      this.boundPreventDefaults = null;
      this.isInitialized = false;
      console.log('⌨️ Keyboard shortcuts destroyed');
    }
  }
}

// Create singleton instance
export const KeyboardShortcuts = new KeyboardShortcutsClass();

// Export for direct use
export default KeyboardShortcuts;
