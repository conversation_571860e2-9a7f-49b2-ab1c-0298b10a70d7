<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Barcode Scanner Fix Test - اختبار إصلاح الباركود</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            direction: rtl;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-input {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: 3px solid #00ff41;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #0f1419, #1a252f);
            color: #00ff41;
            direction: ltr;
        }
        .test-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);
        }
        .test-button {
            padding: 12px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-success { background: #4CAF50; color: white; }
        .btn-warning { background: #ff9800; color: white; }
        .btn-danger { background: #f44336; color: white; }
        .btn-info { background: #2196F3; color: white; }
        .status-panel {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            background: #1a1a1a;
            color: #00ff41;
            border: 2px solid #00ff41;
        }
        .log-container {
            height: 300px;
            overflow-y: auto;
            background: #000;
            color: #00ff41;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 2px solid #00ff41;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .health-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .health-green { background: #4CAF50; }
        .health-yellow { background: #ff9800; }
        .health-red { background: #f44336; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🔧 Barcode Scanner Fix Test</h1>
            <h2>اختبار إصلاح الباركود النهائي</h2>
            <p>Test the definitive fix for barcode scanner keyboard shortcut conflicts</p>
        </div>

        <div class="test-section">
            <h2>📊 System Status - حالة النظام</h2>
            <div id="systemStatus" class="status-panel">
                Loading system status...
            </div>
            <button class="test-button btn-success" onclick="updateSystemStatus()">🔄 Refresh Status</button>
            <button class="test-button btn-warning" onclick="testKeyboardShortcuts()">⌨️ Test Shortcuts</button>
            <button class="test-button btn-danger" onclick="resetSystem()">🔧 Reset System</button>
        </div>

        <div class="test-grid">
            <div class="test-section">
                <h3>📷 Dashboard Scanner Test</h3>
                <input type="text" class="test-input barcode-input" 
                       placeholder="Dashboard barcode scanner - امسح الباركود" 
                       id="dashboard-scanner" 
                       onkeydown="testBarcodeInput(event, 'Dashboard')" 
                       onfocus="handleFocus('Dashboard')"
                       onblur="handleBlur('Dashboard')" />
                <div>Status: <span id="dashboard-status">Ready</span></div>
            </div>

            <div class="test-section">
                <h3>🛒 Sales Scanner Test</h3>
                <input type="text" class="test-input barcode-input sales-scanner" 
                       placeholder="Sales barcode scanner - مسح منتج للفاتورة" 
                       onkeydown="testBarcodeInput(event, 'Sales')"
                       onfocus="handleFocus('Sales')"
                       onblur="handleBlur('Sales')" />
                <div>Status: <span id="sales-status">Ready</span></div>
            </div>

            <div class="test-section">
                <h3>✏️ Edit Scanner Test</h3>
                <input type="text" class="test-input barcode-input edit-scanner" 
                       placeholder="Edit barcode scanner - تعديل الفاتورة" 
                       onkeydown="testBarcodeInput(event, 'Edit')"
                       onfocus="handleFocus('Edit')"
                       onblur="handleBlur('Edit')" />
                <div>Status: <span id="edit-status">Ready</span></div>
            </div>

            <div class="test-section">
                <h3>📦 Product Scanner Test</h3>
                <input type="text" class="test-input barcode-input product-barcode" 
                       placeholder="Product barcode - باركود المنتج" 
                       onkeydown="testBarcodeInput(event, 'Product')"
                       onfocus="handleFocus('Product')"
                       onblur="handleBlur('Product')" />
                <div>Status: <span id="product-status">Ready</span></div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Test Controls - أدوات الاختبار</h2>
            <button class="test-button btn-success" onclick="startComprehensiveTest()">🚀 Start Comprehensive Test</button>
            <button class="test-button btn-info" onclick="testFunctionKeys()">🔧 Test Function Keys</button>
            <button class="test-button btn-warning" onclick="simulateBarcodeScanning()">📷 Simulate Barcode Scanning</button>
            <button class="test-button btn-danger" onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <div class="test-section">
            <h2>📋 Test Log - سجل الاختبار</h2>
            <div id="testLog" class="log-container">
                Test log will appear here...
            </div>
        </div>

        <div class="test-section">
            <h2>📈 Test Results - نتائج الاختبار</h2>
            <div id="testResults" class="status-panel">
                No tests run yet. Click "Start Comprehensive Test" to begin.
            </div>
        </div>
    </div>

    <script>
        // Test state
        let testState = {
            shortcutsEnabled: true,
            barcodeActive: false,
            testCount: 0,
            successCount: 0,
            errorCount: 0,
            startTime: null
        };

        // Initialize barcode shortcut manager simulation
        window.barcodeShortcutManager = {
            isEnabled: true,
            isBarcodeActive: false,
            
            checkBarcodeInput: (target) => {
                if (!target || target.tagName !== 'INPUT') return false;
                
                const barcodeIndicators = [
                    'barcode-input', 'scanner-input', 'dashboard-scanner',
                    'sales-scanner', 'edit-scanner', 'product-barcode'
                ];
                
                if (target.className) {
                    const classNames = target.className.toLowerCase();
                    if (barcodeIndicators.some(indicator => classNames.includes(indicator))) {
                        return true;
                    }
                }
                
                if (target.id) {
                    const id = target.id.toLowerCase();
                    if (barcodeIndicators.some(indicator => id.includes(indicator))) {
                        return true;
                    }
                }
                
                return false;
            },
            
            setShortcutsEnabled: (enabled) => {
                testState.shortcutsEnabled = enabled;
                log(`🔧 BARCODE FIX: Shortcuts ${enabled ? 'enabled' : 'disabled'}`, enabled ? 'success' : 'warning');
            }
        };

        // Simulate KeyboardShortcuts system
        window.KeyboardShortcuts = {
            setEnabled: (enabled) => {
                testState.shortcutsEnabled = enabled;
                log(`⌨️ KeyboardShortcuts: ${enabled ? 'enabled' : 'disabled'}`, enabled ? 'success' : 'warning');
            }
        };

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('testLog');
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        // Handle focus events
        function handleFocus(scanner) {
            if (window.barcodeShortcutManager) {
                window.barcodeShortcutManager.isBarcodeActive = true;
                window.barcodeShortcutManager.setShortcutsEnabled(false);
                testState.barcodeActive = true;
                document.getElementById(`${scanner.toLowerCase()}-status`).innerHTML = 
                    '<span class="health-indicator health-green"></span>Focused - Shortcuts Disabled';
                log(`🔧 ${scanner} scanner focused - shortcuts disabled`, 'success');
            }
        }

        // Handle blur events
        function handleBlur(scanner) {
            setTimeout(() => {
                if (window.barcodeShortcutManager && !window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)) {
                    window.barcodeShortcutManager.isBarcodeActive = false;
                    window.barcodeShortcutManager.setShortcutsEnabled(true);
                    testState.barcodeActive = false;
                    document.getElementById(`${scanner.toLowerCase()}-status`).innerHTML = 
                        '<span class="health-indicator health-yellow"></span>Ready';
                    log(`🔧 ${scanner} scanner blurred - shortcuts re-enabled`, 'info');
                }
            }, 100);
        }

        // Test barcode input
        function testBarcodeInput(event, scanner) {
            const value = event.target.value;
            log(`📷 ${scanner} scanner input: "${value}" (Key: ${event.key})`, 'info');
            
            if (event.key === 'Enter') {
                log(`✅ ${scanner} scanner Enter pressed - processing barcode: "${value}"`, 'success');
                testState.testCount++;
                testState.successCount++;
                event.target.value = '';
                updateTestResults();
            }
            
            // Test function keys during barcode input
            if (event.key.startsWith('F') && event.key.length <= 3) {
                if (testState.shortcutsEnabled) {
                    log(`❌ CONFLICT: Function key ${event.key} processed while barcode input active!`, 'error');
                    testState.errorCount++;
                } else {
                    log(`✅ SUCCESS: Function key ${event.key} blocked during barcode input`, 'success');
                    testState.successCount++;
                }
                testState.testCount++;
                updateTestResults();
            }
        }

        // Update system status
        function updateSystemStatus() {
            const status = document.getElementById('systemStatus');
            const shortcutStatus = testState.shortcutsEnabled ? 
                '<span class="success">✅ Enabled</span>' : 
                '<span class="warning">⚠️ Disabled</span>';
            const barcodeStatus = testState.barcodeActive ? 
                '<span class="warning">⚠️ Active</span>' : 
                '<span class="success">✅ Inactive</span>';
            
            status.innerHTML = `
                <strong>🔧 Barcode Scanner Fix Status:</strong><br>
                Keyboard Shortcuts: ${shortcutStatus}<br>
                Barcode Input Active: ${barcodeStatus}<br>
                Tests Run: ${testState.testCount}<br>
                Success Rate: ${testState.testCount > 0 ? Math.round((testState.successCount / testState.testCount) * 100) : 0}%<br>
                System Health: <span class="health-indicator ${testState.errorCount < 3 ? 'health-green' : testState.errorCount < 10 ? 'health-yellow' : 'health-red'}"></span>
                ${testState.errorCount < 3 ? 'Healthy' : testState.errorCount < 10 ? 'Warning' : 'Critical'}
            `;
            log('📊 System status updated', 'info');
        }

        // Test keyboard shortcuts
        function testKeyboardShortcuts() {
            log('⌨️ Testing keyboard shortcuts...', 'info');
            const functionKeys = ['F1', 'F2', 'F3', 'F4', 'F5'];
            
            functionKeys.forEach(key => {
                if (testState.shortcutsEnabled) {
                    log(`✅ ${key} shortcut would be processed (shortcuts enabled)`, 'success');
                } else {
                    log(`🚫 ${key} shortcut blocked (shortcuts disabled for barcode)`, 'warning');
                }
            });
        }

        // Start comprehensive test
        function startComprehensiveTest() {
            log('🚀 Starting comprehensive barcode scanner test...', 'info');
            testState.startTime = Date.now();
            testState.testCount = 0;
            testState.successCount = 0;
            testState.errorCount = 0;
            
            // Test sequence
            setTimeout(() => testFunctionKeys(), 1000);
            setTimeout(() => simulateBarcodeScanning(), 2000);
            setTimeout(() => testFocusBlurSequence(), 3000);
            setTimeout(() => completeComprehensiveTest(), 5000);
        }

        // Test function keys
        function testFunctionKeys() {
            log('🔧 Testing function key behavior...', 'info');
            const keys = ['F1', 'F2', 'F3', 'F4', 'F5'];
            keys.forEach(key => {
                const shouldBeBlocked = testState.barcodeActive;
                if (shouldBeBlocked && !testState.shortcutsEnabled) {
                    log(`✅ ${key} correctly blocked during barcode input`, 'success');
                    testState.successCount++;
                } else if (!shouldBeBlocked && testState.shortcutsEnabled) {
                    log(`✅ ${key} correctly allowed when no barcode input`, 'success');
                    testState.successCount++;
                } else {
                    log(`❌ ${key} behavior incorrect`, 'error');
                    testState.errorCount++;
                }
                testState.testCount++;
            });
        }

        // Simulate barcode scanning
        function simulateBarcodeScanning() {
            log('📷 Simulating barcode scanning...', 'info');
            const testCodes = ['123456789', 'ABC123DEF', '000000001', 'PROD2024'];
            const scanners = ['dashboard-scanner'];
            
            testCodes.forEach((code, index) => {
                setTimeout(() => {
                    const scanner = document.getElementById(scanners[0]);
                    scanner.focus();
                    scanner.value = code;
                    log(`📷 Simulated scan: ${code}`, 'info');
                    
                    // Simulate Enter key
                    setTimeout(() => {
                        const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
                        scanner.dispatchEvent(enterEvent);
                    }, 100);
                }, index * 500);
            });
        }

        // Test focus/blur sequence
        function testFocusBlurSequence() {
            log('🎯 Testing focus/blur sequence...', 'info');
            const inputs = document.querySelectorAll('.barcode-input');
            
            inputs.forEach((input, index) => {
                setTimeout(() => {
                    input.focus();
                    setTimeout(() => {
                        input.blur();
                    }, 200);
                }, index * 400);
            });
        }

        // Complete comprehensive test
        function completeComprehensiveTest() {
            const duration = Date.now() - testState.startTime;
            const successRate = testState.testCount > 0 ? Math.round((testState.successCount / testState.testCount) * 100) : 0;
            
            log(`🎉 Comprehensive test completed in ${duration}ms`, 'success');
            log(`📊 Results: ${testState.successCount}/${testState.testCount} tests passed (${successRate}%)`, 
                successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error');
            
            updateTestResults();
        }

        // Update test results
        function updateTestResults() {
            const results = document.getElementById('testResults');
            const successRate = testState.testCount > 0 ? Math.round((testState.successCount / testState.testCount) * 100) : 0;
            const status = successRate >= 90 ? 'EXCELLENT' : successRate >= 70 ? 'GOOD' : 'NEEDS IMPROVEMENT';
            const statusClass = successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error';
            
            results.innerHTML = `
                <div class="${statusClass}">
                    <strong>Test Results:</strong><br>
                    Total Tests: ${testState.testCount}<br>
                    Successful: ${testState.successCount}<br>
                    Failed: ${testState.errorCount}<br>
                    Success Rate: ${successRate}%<br>
                    Status: ${status}<br>
                    ${successRate >= 90 ? '🎉 Barcode scanner fix is working perfectly!' : 
                      successRate >= 70 ? '⚠️ Barcode scanner fix is mostly working' : 
                      '❌ Barcode scanner fix needs attention'}
                </div>
            `;
        }

        // Reset system
        function resetSystem() {
            log('🔧 Resetting test system...', 'warning');
            testState = {
                shortcutsEnabled: true,
                barcodeActive: false,
                testCount: 0,
                successCount: 0,
                errorCount: 0,
                startTime: null
            };
            
            // Reset all input fields
            document.querySelectorAll('.test-input').forEach(input => {
                input.value = '';
                input.blur();
            });
            
            // Reset status displays
            ['dashboard', 'sales', 'edit', 'product'].forEach(scanner => {
                document.getElementById(`${scanner}-status`).innerHTML = 'Ready';
            });
            
            updateSystemStatus();
            updateTestResults();
            log('✅ System reset completed', 'success');
        }

        // Clear log
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('📋 Test log cleared', 'info');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('🔧 Barcode Scanner Fix Test initialized', 'success');
            updateSystemStatus();
            updateTestResults();
        });

        // Global keyboard event listener for testing
        document.addEventListener('keydown', (event) => {
            if (event.key.startsWith('F') && event.key.length <= 3) {
                if (testState.barcodeActive && !testState.shortcutsEnabled) {
                    event.preventDefault();
                    log(`🚫 Function key ${event.key} blocked (barcode input active)`, 'success');
                } else {
                    log(`⌨️ Function key ${event.key} allowed (no barcode input)`, 'info');
                }
            }
        });
    </script>
</body>
</html>
